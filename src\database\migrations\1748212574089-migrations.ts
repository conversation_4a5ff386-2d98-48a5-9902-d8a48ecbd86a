import { MigrationInterface, QueryRunner } from "typeorm";

export class Migrations1748212574089 implements MigrationInterface {
    name = 'Migrations1748212574089'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."state_election_status_enum" AS ENUM('pending', 'active', 'completed')`);
        await queryRunner.query(`CREATE TABLE "state_election" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "status" "public"."state_election_status_enum" NOT NULL DEFAULT 'pending', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "startedAt" TIMESTAMP, "endedAt" TIMESTAMP, "candidates" json NOT NULL DEFAULT '[]', "voters" json NOT NULL DEFAULT '[]', "stateId" uuid, "winnerId" integer, CONSTRAINT "PK_1fbeb5fb3cf700773fae40c5bea" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TYPE "public"."user_activeautomode_enum" ADD VALUE 'travel'`);
        await queryRunner.query(`ALTER TABLE "state_election" ADD CONSTRAINT "FK_9396356395ce2d2614f01d77c1b" FOREIGN KEY ("stateId") REFERENCES "state"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "state_election" ADD CONSTRAINT "FK_844b33f95afe9afc493b9930542" FOREIGN KEY ("winnerId") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "state_election" DROP CONSTRAINT "FK_844b33f95afe9afc493b9930542"`);
        await queryRunner.query(`ALTER TABLE "state_election" DROP CONSTRAINT "FK_9396356395ce2d2614f01d77c1b"`);
        await queryRunner.query(`DROP TABLE "state_election"`);
        await queryRunner.query(`DROP TYPE "public"."state_election_status_enum"`);
    }

}
