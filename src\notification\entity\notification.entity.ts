import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
} from 'typeorm';

import { User } from '../../user/entity/user.entity';

export enum NotificationType {
  WAR_DECLARED = 'war_declared',
  WAR_JOINED = 'war_joined',
  WAR_PHASE_CHANGED = 'war_phase_changed',
  WAR_ENDED = 'war_ended',
  WAR_DAMAGE_MILESTONE = 'war_damage_milestone',
  REGION_CONQUERED = 'region_conquered',
  RESOURCES_CAPTURED = 'resources_captured',
  REVOLUTION_SUCCEEDED = 'revolution_succeeded',
  STATE_ELECTION = 'state_election',
  SYSTEM = 'system',
}

@Entity()
export class Notification {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => User)
  user: User;

  @Column({
    type: 'enum',
    enum: NotificationType,
  })
  type: NotificationType;

  @Column()
  title: string;

  @Column('text')
  content: string;

  @Column({ nullable: true })
  entityId: string;

  @Column({ nullable: true })
  entityType: string;

  @Column({ default: false })
  isRead: boolean;

  @CreateDateColumn()
  createdAt: Date;
}
