import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, In } from 'typeorm';
import { Travel, TravelStatus } from './entity/travel.entity';
import {
  TravelPermission,
  TravelPermissionStatus,
} from './entity/travel-permission.entity';
import { User } from '../user/entity/user.entity';
import { Region, RegionStatus } from '../region/entity/region.entity';
import { State } from '../state/entity/state.entity';
import {
  calculateHaversineDistance,
  calculateTravelTime,
} from './utils/distance.utils';
import {
  InitiateTravelDto,
  RequestTravelPermissionDto,
  RespondToPermissionRequestDto,
  TravelTimeEstimateDto,
} from './dto/initiate-travel.dto';
import { Cron } from '@nestjs/schedule';
import { TravelAutoService } from './travel-auto.service';
import { StateService } from 'src/state/state.service';
import { AutoMode } from 'src/user/enums/auto-mode.enum';

@Injectable()
export class TravelService {
  private readonly logger = new Logger(TravelService.name);

  constructor(
    @InjectRepository(Travel)
    private travelRepository: Repository<Travel>,
    @InjectRepository(TravelPermission)
    private travelPermissionRepository: Repository<TravelPermission>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Region)
    private regionRepository: Repository<Region>,
    @InjectRepository(State)
    private stateRepository: Repository<State>,
    private travelAutoService: TravelAutoService,
    private stateService: StateService
  ) {}

  /**
   * Calculate travel time between two regions
   */
  async calculateTravelTime(
    sourceRegionId: string,
    destinationRegionId: string,
  ): Promise<{
    distance: number;
    travelTime: number;
    seaCrossing: boolean;
    sameState: boolean;
  }> {
    // Get the source and destination regions
    const sourceRegion = await this.regionRepository.findOne({
      where: { id: sourceRegionId },
      relations: ['state'],
    });
    const destinationRegion = await this.regionRepository.findOne({
      where: { id: destinationRegionId },
      relations: ['state'],
    });

    if (!sourceRegion || !destinationRegion) {
      throw new NotFoundException('Source or destination region not found');
    }

    // Check if both regions have latitude and longitude
    if (
      !sourceRegion.latitude ||
      !sourceRegion.longitude ||
      !destinationRegion.latitude ||
      !destinationRegion.longitude
    ) {
      throw new BadRequestException(
        'Source or destination region does not have geographic coordinates',
      );
    }

    // Calculate distance using Haversine formula
    const distance = calculateHaversineDistance(
      sourceRegion.latitude,
      sourceRegion.longitude,
      destinationRegion.latitude,
      destinationRegion.longitude,
    );

    // Determine if travel crosses sea
    const seaCrossing = !!(
      sourceRegion.seaAccess &&
      destinationRegion.seaAccess &&
      (!sourceRegion.bordersWith ||
        !sourceRegion.bordersWith.includes(destinationRegion.countryCode))
    );

    // Determine if travel is within the same state
    const sameState = !!(
      sourceRegion.state &&
      destinationRegion.state &&
      sourceRegion.state.id === destinationRegion.state.id
    );

    // Calculate travel time
    const travelTime = calculateTravelTime(distance, seaCrossing, sameState);

    return {
      distance,
      travelTime,
      seaCrossing,
      sameState,
    };
  }

  /**
   * Check if a user has permission to travel to a region
   */
  async checkTravelPermission(
    userId: number,
    sourceRegionId: string,
    destinationRegionId: string,
  ): Promise<{
    requiresPermission: boolean;
    hasPermission: boolean;
    permissionId?: string;
  }> {
    // Get the source and destination regions
    const sourceRegion = await this.regionRepository.findOne({
      where: { id: sourceRegionId },
      relations: ['state'],
    });
    const destinationRegion = await this.regionRepository.findOne({
      where: { id: destinationRegionId },
      relations: ['state'],
    });

    if (!sourceRegion || !destinationRegion) {
      throw new NotFoundException('Source or destination region not found');
    }

    // No permission required if:
    // 1. Moving between regions of the same state
    // 2. Moving to/from independent regions
    const sameState =
      sourceRegion.state &&
      destinationRegion.state &&
      sourceRegion.state.id === destinationRegion.state.id;

    const toIndependent =
      destinationRegion.status === RegionStatus.INDEPENDENT;
    const fromIndependent = sourceRegion.status === RegionStatus.INDEPENDENT;
    const state = await this.stateService.findStateLeader(userId);
    const isLeader = state ? true : false;
    console.log(isLeader,'isLeader');
    
    
    if (sameState || toIndependent || fromIndependent || isLeader) {
      return {
        requiresPermission: false,
        hasPermission: true,
      };
    }

    // Check if the user has an approved permission
    const permission = await this.travelPermissionRepository.findOne({
      where: {
        user: { id: userId },
        sourceRegion: { id: sourceRegionId },
        destinationRegion: { id: destinationRegionId },
        status: TravelPermissionStatus.APPROVED,
      },
      order: { createdAt: 'DESC' },
    });

    return {
      requiresPermission: true,
      hasPermission: !!permission,
      permissionId: permission?.id,
    };
  }

  /**
   * Request permission to travel to a region
   */
  async requestTravelPermission(
    userId: number,
    requestTravelPermissionDto: RequestTravelPermissionDto,
  ): Promise<TravelPermission> {
    const { destinationRegionId, reason } = requestTravelPermissionDto;

    // Get the user and their current region
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['region', 'region.state'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get the destination region
    const destinationRegion = await this.regionRepository.findOne({
      where: { id: destinationRegionId },
      relations: ['state'],
    });

    if (!destinationRegion) {
      throw new NotFoundException('Destination region not found');
    }

    // Check if destination region belongs to a state
    if (
      !destinationRegion.state ||
      destinationRegion.status === RegionStatus.INDEPENDENT
    ) {
      throw new BadRequestException(
        'Cannot request permission for an independent region',
      );
    }

    // Check if user is already traveling
    if (user.isTraveling) {
      throw new BadRequestException('User is already traveling');
    }

    // Check if there's already a pending request
    const existingRequest = await this.travelPermissionRepository.findOne({
      where: {
        user: { id: userId },
        destinationRegion: { id: destinationRegionId },
        status: TravelPermissionStatus.PENDING,
      },
    });

    if (existingRequest) {
      throw new BadRequestException(
        'You already have a pending request for this region',
      );
    }

    // Create a new permission request
    const permissionRequest = this.travelPermissionRepository.create({
      user,
      sourceRegion: user.region,
      destinationRegion,
      destinationState: destinationRegion.state,
      reason,
      status: TravelPermissionStatus.PENDING,
    });

    return this.travelPermissionRepository.save(permissionRequest);
  }

  /**
   * Respond to a travel permission request (approve/reject)
   */
  async respondToPermissionRequest(
    userId: number,
    permissionId: string,
    respondToPermissionRequestDto: RespondToPermissionRequestDto,
  ): Promise<TravelPermission> {
    const { approve, responseMessage } = respondToPermissionRequestDto;

    // Get the permission request
    const permissionRequest = await this.travelPermissionRepository.findOne({
      where: { id: permissionId },
      relations: [
        'user',
        'sourceRegion',
        'destinationRegion',
        'destinationState',
        'destinationState.leader',
      ],
    });

    if (!permissionRequest) {
      throw new NotFoundException('Permission request not found');
    }

    // Check if the user is the leader of the destination state
    if (
      !permissionRequest.destinationState ||
      permissionRequest.destinationState.leader?.id !== userId
    ) {
      throw new ForbiddenException(
        'Only the leader of the destination state can respond to this request',
      );
    }

    // Check if the request is still pending
    if (permissionRequest.status !== TravelPermissionStatus.PENDING) {
      throw new BadRequestException(
        'This permission request has already been processed',
      );
    }

    // Get the responding user
    const respondingUser = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!respondingUser) {
      throw new NotFoundException('Responding user not found');
    }

    // Update the permission request
    permissionRequest.status = approve
      ? TravelPermissionStatus.APPROVED
      : TravelPermissionStatus.REJECTED;

    // Handle the optional responseMessage
    if (responseMessage !== undefined) {
      permissionRequest.responseMessage = responseMessage;
    }

    permissionRequest.respondedBy = respondingUser;
    permissionRequest.respondedAt = new Date();

    // Save the updated permission request
    const savedPermission = await this.travelPermissionRepository.save(permissionRequest);

    // If the permission was approved, automatically start the travel
    if (approve) {
      try {
        // Check if the user is already traveling
        const requestingUser = await this.userRepository.findOne({
          where: { id: permissionRequest.user.id },
          relations: ['region'],
        });

        if (!requestingUser) {
          throw new NotFoundException('Requesting user not found');
        }

        if (requestingUser.isTraveling) {
          this.logger.warn(`User ${requestingUser.id} is already traveling, cannot auto-start travel`);
          return savedPermission;
        }

        // Calculate travel details
        const travelDetails = await this.calculateTravelTime(
          permissionRequest.sourceRegion.id,
          permissionRequest.destinationRegion.id,
        );

        // Create a new travel record
        const now = new Date();
        const endTime = new Date(
          now.getTime() + travelDetails.travelTime * 60 * 1000,
        );

        const travel = this.travelRepository.create({
          user: permissionRequest.user,
          sourceRegion: permissionRequest.sourceRegion,
          destinationRegion: permissionRequest.destinationRegion,
          startTime: now,
          endTime,
          distance: travelDetails.distance,
          travelTime: travelDetails.travelTime,
          seaCrossing: travelDetails.seaCrossing,
          sameState: travelDetails.sameState,
          status: TravelStatus.IN_PROGRESS,
        });

        // Update user's traveling status
        requestingUser.isTraveling = true;
        await this.userRepository.save(requestingUser);

        // Save the travel
        await this.travelRepository.save(travel);

        this.logger.log(`Auto-started travel for user ${requestingUser.id} after permission approval`);
      } catch (error) {
        // Log the error but don't fail the permission response
        this.logger.error(
          `Error auto-starting travel after permission approval: ${error.message}`,
          error.stack,
        );
      }
    }

    return savedPermission;
  }

  /**
   * Initiate travel between regions
   */
  async initiateTravel(
    userId: number,
    initiateTravelDto: InitiateTravelDto,
  ): Promise<Travel> {
    const { destinationRegionId, permissionId } = initiateTravelDto;

    // Get the user and their current region
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['region', 'region.state'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if user is already traveling
    if (user.isTraveling) {
      throw new BadRequestException('You are already traveling');
    }

    if(user.activeAutoMode !== AutoMode.NONE){
      throw new BadRequestException('You cannot travel while in auto mode');
    }

    // Get the destination region
    const destinationRegion = await this.regionRepository.findOne({
      where: { id: destinationRegionId },
      relations: ['state'],
    });

    if (!destinationRegion) {
      throw new NotFoundException('Destination region not found');
    }

    // Check if the user is trying to travel to the same region
    if (user.region.id === destinationRegion.id) {
      throw new BadRequestException('Cannot travel to the same region');
    }

    // Check if the user has permission to travel
    const permissionCheck = await this.checkTravelPermission(
      userId,
      user.region.id,
      destinationRegion.id,
    );

    if (permissionCheck.requiresPermission) {
      if (!permissionCheck.hasPermission) {
        throw new ForbiddenException(
          'You do not have permission to travel to this region',
        );
      }

      // If permission is required, verify the provided permissionId
      if (!permissionId || permissionId !== permissionCheck.permissionId) {
        throw new BadRequestException('Invalid permission ID');
      }
    }

    // Calculate travel details
    const travelDetails = await this.calculateTravelTime(
      user.region.id,
      destinationRegion.id,
    );

    // Create a new travel record
    const now = new Date();
    const endTime = new Date(
      now.getTime() + travelDetails.travelTime * 60 * 1000,
    );

    const travel = this.travelRepository.create({
      user,
      sourceRegion: user.region,
      destinationRegion,
      startTime: now,
      endTime,
      distance: travelDetails.distance,
      travelTime: travelDetails.travelTime,
      seaCrossing: travelDetails.seaCrossing,
      sameState: travelDetails.sameState,
      status: TravelStatus.IN_PROGRESS,
    });

    // Update user's traveling status
    user.isTraveling = true;
    await this.userRepository.save(user);

    const savedTravel = await this.travelRepository.save(travel);

    // Schedule travel completion using TravelAutoService
    await this.travelAutoService.scheduleTravelCompletion(userId, savedTravel.id, endTime);
    
    // Log for debugging
    this.logger.log(`Travel initiated: User ${userId} traveling from ${user.region.id} to ${destinationRegion.id}, scheduled to complete at ${endTime}`);

    return savedTravel;
  }

  /**
   * Get current travel for a user
   */
  async getCurrentTravel(userId: number): Promise<Travel | null> {
    return this.travelRepository.findOne({
      where: {
        user: { id: userId },
        status: TravelStatus.IN_PROGRESS,
      },
      relations: ['sourceRegion', 'destinationRegion'],
    });
  }

  /**
   * Cancel an in-progress travel
   */
  async cancelTravel(userId: number, travelId: string): Promise<Travel> {
    // Get the travel
    const travel = await this.travelRepository.findOne({
      where: {
        id: travelId,
        user: { id: userId },
        status: TravelStatus.IN_PROGRESS,
      },
      relations: ['user'],
    });

    if (!travel) {
      throw new NotFoundException('Travel not found or already completed');
    }

    // Update travel status
    travel.status = TravelStatus.CANCELLED;
    await this.travelRepository.save(travel);

    // Update user's traveling status
    const user = travel.user;
    user.isTraveling = false;
    await this.userRepository.save(user);

    return travel;
  }

  /**
   * Complete travels that have reached their end time
   * This is run as a scheduled task every minute
   */
  @Cron('0 * * * * *') // Run every minute
  async completeScheduledTravels(): Promise<void> {
    this.logger.log('Checking for completed travels');

    try {
      // Find all in-progress travels that have reached their end time
      const completedTravels = await this.travelRepository.find({
        where: {
          status: TravelStatus.IN_PROGRESS,
          endTime: LessThan(new Date()),
        },
        relations: ['user', 'destinationRegion'],
      });

      this.logger.log(`Found ${completedTravels.length} completed travels`);

      // Process each completed travel
      for (const travel of completedTravels) {
        try {
          // Update travel status
          travel.status = TravelStatus.COMPLETED;
          await this.travelRepository.save(travel);

          // Update user's region and traveling status
          const user = travel.user;
          if (user) {
            user.region = travel.destinationRegion;
            user.isTraveling = false;
            await this.userRepository.save(user);

            this.logger.log(
              `Completed travel for user ${user.id} to region ${travel.destinationRegion.id}`,
            );
          }
        } catch (travelError) {
          this.logger.error(
            `Error completing travel ${travel.id}: ${travelError.message}`,
            travelError.stack,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Error completing scheduled travels: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Get travel time estimate between two regions
   */
  async getTravelTimeEstimate(
    travelTimeEstimateDto: TravelTimeEstimateDto,
  ): Promise<{
    distance: number;
    travelTime: number;
    seaCrossing: boolean;
    sameState: boolean;
  }> {
    const { originRegionId, destinationRegionId } = travelTimeEstimateDto;
    return this.calculateTravelTime(originRegionId, destinationRegionId);
  }

  /**
   * Get all permission requests for a state leader
   */
  async getStatePermissionRequests(
    userId: number,
  ): Promise<TravelPermission[]> {
    // Get the user and check if they are a state leader
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Find states where the user is the leader
    const states = await this.stateRepository.find({
      where: { leader: { id: userId } },
    });

    if (states.length === 0) {
      return [];
    }

    // Get all permission requests for the user's states
    return this.travelPermissionRepository.find({
      where: {
        destinationState: { id: In(states.map((state) => state.id)) },
        status: TravelPermissionStatus.PENDING,
      },
      relations: ['user', 'sourceRegion', 'destinationRegion'],
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Get all permission requests for a user
   */
  async getUserPermissionRequests(
    userId: number,
  ): Promise<TravelPermission[]> {
    return this.travelPermissionRepository.find({
      where: {
        user: { id: userId },
      },
      relations: ['sourceRegion', 'destinationRegion', 'respondedBy'],
      order: { createdAt: 'DESC' },
    });
  }
}



